<?php

namespace App\Models;

use App\Models\Event;
use App\Models\ChannelItem;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Channel extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = ['swap_event_id','event_id','title','top_banner','background'];

    public function getTopBannerAttribute($value)
    {
        return $value ? Storage::disk('s3')->url($value) : null;
    }

    public function topBannerPath()
    {
        $storageUrl = Storage::url('');
        return $this->top_banner ? str_replace($storageUrl, '', $this->top_banner) : null;
    }

    public function getBackgroundAttribute($value)
    {
        return $value ? Storage::disk('s3')->url($value) : null;
    }

    public function backgroundPath()
    {
        $storageUrl = Storage::url('');
        return $this->background ? str_replace($storageUrl, '', $this->background) : null;
    }

    // Relationships
    public function items()
    {
        return $this->hasMany(ChannelItem::class)->orderBy('order');
    }

    public function event()
    {
        return $this->belongsTo(Event::class);
    }
}
