<?php

namespace App\Models;

use App\Models\Channel;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Session\Store;
use Illuminate\Support\Facades\Storage;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ChannelItem extends Model
{
    use HasFactory, SoftDeletes;
    protected $fillable = ['channel_id', 'type', 'time', 'media_url', 'thumb', 'order', 'swap_place_id', 'swap_place_name'];

    public function getMediaUrlAttribute($value)
    {
        return $value ? Storage::disk('s3')->url($value) : null;
    }

    public function getThumbAttribute($value)
    {
        return $value ? Storage::disk('s3')->url($value) : null;
    }

    public function mediaPath()
    {
        $storageUrl = Storage::url('');
        return $this->media_url ? str_replace($storageUrl, '', $this->media_url) : null;
    }

    public function thumbPath()
    {
        $storageUrl = Storage::url('');
        return $this->thumb ? str_replace($storageUrl, '', $this->thumb) : null;
    }

    public static function boot()
    {
        parent::boot();
        static::deleting(function ($channelItem) {
            if ($channelItem->media_url) {
                Storage::delete($channelItem->mediaPath());
            }
        });
    }

    // Relationships
    public function channel()
    {
        return $this->belongsTo(Channel::class);
    }
}
