<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SwapcardEvents extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'event_id',
        'title',
        'banner_url',
        'begins_at',
        'ends_at',
        'total_plannings',
        'info',
        'places',
        'is_live',
    ];

    protected $casts = [
        'info' => 'json',
        'places' => 'json',
        'is_live' => 'boolean',
    ];
}
