<?php

namespace App\Jobs;

use App\Models\ChannelItem;
use App\Traits\ImageTrait;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class MoveFilesToDigitalOceanJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, ImageTrait;

    public $type;
    public $filePath;
    public $channelId;
    public $itemId;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($type, $filePath, $channelId, $itemId = null)
    {
        $this->type = $type;
        $this->filePath = $filePath;
        $this->channelId = $channelId;
        $this->itemId = $itemId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            if (Storage::disk('vultr')->exists($this->filePath)) {
                $image = Storage::disk('vultr')->get($this->filePath);
                if ($this->type == 'top-banner' || $this->type == 'background') {
                    $this->uploadChannelImage($image, $this->channelId, $this->type);
                } elseif ($this->type == 'item') {
                    $images = $this->uploadChannelItemImage($image, $this->channelId);
                    if (isset($images['media_url']) && isset($images['thumb'])) {
                        ChannelItem::find($this->itemId)->update([
                            'media_url' => $images['media_url'],
                            'thumb' => $images['thumb']
                        ]);
                    } else {
                        Log::error('Error uploading image to digital ocean', json_encode($images));
                    }
                }
            }
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }
}
