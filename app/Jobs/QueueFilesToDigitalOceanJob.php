<?php

namespace App\Jobs;

use App\Models\Channel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class QueueFilesToDigitalOceanJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $channels = Channel::with(['items' => function ($query) {
            $query->where('type', 'banner')->whereNotNull('media_url');
        }])->get();

        foreach ($channels as $channel) {
            if ($channel->top_banner) {
                MoveFilesToDigitalOceanJob::dispatch('top-banner', $channel->topBannerPath(), $channel->id)->onQueue('default');
            }

            if ($channel->background) {
                MoveFilesToDigitalOceanJob::dispatch('background', $channel->backgroundPath(), $channel->id)->onQueue('default');
            }

            foreach ($channel->items as $item) {
                MoveFilesToDigitalOceanJob::dispatch('item', $item->mediaPath(), $channel->id, $item->id)->onQueue('default');
            }
        }
    }
}
