<?php

namespace App\Services;

use App\Models\SwapcardEvents;
use App\Models\SwapcardSyncLog;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SwapcardService
{
    /**
     * Get events from Swapcard API
     *
     * @return array
     */
    public function getEvents(): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => config('services.swapcard.key'),
                'Content-Type' => 'application/json'
            ])->post(
                config('services.swapcard.uri'),
                [
                    'query' => 'query GetEvents {
                    events(pageSize: 200, page: 1){
                        id
                        title
                        bannerUrl
                        beginsAt
                        endsAt
                        totalPlannings
                        isLive
                        planningsPlaces {
                            id
                            name
                            value
                        }
                    }
                }']
            );
            return collect($response->json()['data']['events'])->sortBy('title')->toArray();
        } catch (\Exception $e) {
            Log::error('Error on get events: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Sync events from Swapcard API to database
     *
     * @param string $source Source of the sync (manual, job, api)
     * @return array
     */
    public function syncEvents(string $source = 'manual'): array
    {
        $result = [
            'total_events' => 0,
            'total_synced' => 0,
            'status' => true,
            'error_message' => null
        ];

        try {
            $events = $this->getEvents();
            $result['total_events'] = count($events);

            foreach ($events as $event) {
                SwapcardEvents::updateOrCreate(
                    ['event_id' => $event['id']],
                    [
                        'event_id' => $event['id'],
                        'title' => $event['title'],
                        'banner_url' => $event['bannerUrl'],
                        'begins_at' => $event['beginsAt'],
                        'ends_at' => $event['endsAt'],
                        'total_plannings' => $event['totalPlannings'],
                        'places' => $event['planningsPlaces'],
                        'is_live' => $event['isLive']
                    ]
                );
            }

            $result['total_synced'] = SwapcardEvents::count();
        } catch (\Exception $e) {
            Log::error('Error on sync events: ' . $e->getMessage());
            $result['status'] = false;
            $result['error_message'] = $e->getMessage();
        }

        // Registra o log de sincronização
        SwapcardSyncLog::create([
            'total_events' => $result['total_events'],
            'total_synced' => $result['total_synced'],
            'status' => $result['status'],
            'error_message' => $result['error_message'],
            'source' => $source
        ]);

        return $result;
    }

    /**
     * Get translation value by language code
     *
     * @param array $translations
     * @param string $language
     * @return string|null
     */
    private function getTranslationByLanguage(array $translations, string $language): ?string
    {
        foreach ($translations as $translation) {
            if ($translation['language'] === $language) {
                return $translation['value'];
            }
        }

        return null;
    }

    /**
     * Get plannings by place and event
     *
     * @param string $eventId
     * @param string $placeId
     * @return array
     */
    public function getPlanningsByPlace(string $eventId, string $placeId): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => config('services.swapcard.key'),
                'Content-Type' => 'application/json'
            ])->post(
                config('services.swapcard.uri'),
                ['query' => 'query listPlanningsLocation { plannings(eventId: "'.$eventId.'", pageSize: 100, page: 1, sort: {field: BEGINS_AT, order: ASC}, filters: {placeIds: ["'.$placeId.'"]}) {
                    title
                    beginsAt
                    endsAt
                    format
                    place
                    description
                    titleTranslations {
                        value
                        language 
                    }
                    descriptionTranslations {
                        value
                        language
                    }
                    speakers {
                        firstName
                        photoUrl
                        lastName
                    }
                    exhibitors {
                        logoUrl
                        name
                    }
                }}']
            );

            Log::info('Swapcard response', $response->json());

            if (!isset($response->json()['data']['plannings'])) {
                return [];
            }

            $plannings = collect($response->json()['data']['plannings'])->filter(function ($value, $key) {
                return $value['format'] == 'PHYSICAL' || $value['format'] == 'LIVE_STREAM';
            });

            $allPlannings = [];
            foreach ($plannings->all() as $value) {
                $value['title'] = $this->getTranslationByLanguage($value['titleTranslations'], 'pt_BR') ?? $value['title'];
                $value['description'] = $this->getTranslationByLanguage($value['descriptionTranslations'], 'pt_BR') ?? $value['description'];
                $allPlannings[] = [...$value, 'date' => substr($value['beginsAt'], 0, 10)];
            }

            return $allPlannings;
        } catch (\Exception $e) {
            Log::error('Error on get plannings: ' . $e->getMessage());
            return [];
        }
    }
}
