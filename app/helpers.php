<?php

use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

if (!function_exists('eventInfo')) {
    function eventInfo()
    {
        return cache()->get('event');
    }
}

if (!function_exists('paginateCollection')) {
    /**
     * Paginate a collection.
     *
     * @param Collection $collection
     * @param int $perPage
     * @param int|null $page
     * @param string $pageName
     * @return LengthAwarePaginator
     */
    function paginateCollection(Collection $collection, int $perPage = 15, int $page = null, string $pageName = 'page'): LengthAwarePaginator
    {
        $page = $page ?: LengthAwarePaginator::resolveCurrentPage($pageName);

        return new LengthAwarePaginator(
            $collection->forPage($page, $perPage),
            $collection->count(),
            $perPage,
            $page,
            [
                'path' => LengthAwarePaginator::resolveCurrentPath(),
                'pageName' => $pageName,
            ]
        );
    }
}
