<?php

namespace App\Traits;

use Intervention\Image\ImageManager;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Drivers\Gd\Driver;
use Intervention\Image\Drivers\Imagick\Driver as ImagickDriver;
use Illuminate\Support\Facades\Log;

trait ImageTrait
{
    public function getMediaUrlAttribute($value)
    {
        return $value ? Storage::url($value) : null;
    }

    protected function resizeImage($file, $width = 1080, $height = 1920, $manager = null)
    {
        try {
            if (!$manager) {
                $manager = new ImageManager(new \Intervention\Image\Drivers\Gd\Driver());
            }
    
            return $manager->read($file)->resize($width, $height);
        } catch (\Exception $e) {
            Log::error('Image resize failed', ['exception' => $e]);
            return null;
        }
    }

    public function uploadImage($file, $folder)
    {
        try {
            $image = $this->resizeImage($file);
            if (!$image) {
                throw new \Exception('Failed to process image');
            }
            $encoded = (string) $image->toJpeg(80);
            $path = env('ASSETS_FOLDER').'/'.$folder.'/'.now()->timestamp.'.jpg';
            Storage::put($path, $encoded);
            return $path;
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    public function uploadChannelItemImage($file, $folder)
    {
        try {
            $manager = new ImageManager(new Driver());
    
            // Resize da imagem principal
            $image = $this->resizeImage($file, 1080, 1920, $manager);
            if (!$image) {
                throw new \Exception('Failed to process image');
            }
    
            $encoded = $image->toJpeg(80)->toString();
            $path = env('ASSETS_FOLDER').'/channel-'.$folder.'/items/'.now()->timestamp.'.jpg';
    
            Storage::disk('s3')->put($path, $encoded);
    
            // Resize da thumb
            $thumbImage = $this->resizeImage($file, 300, 300, $manager);
            if (!$thumbImage) {
                throw new \Exception('Failed to process thumb');
            }
    
            $thumbEncoded = $thumbImage->toJpeg(80)->toString();
            $pathThumb = env('ASSETS_FOLDER').'/channel-'.$folder.'/items/thumb-'.now()->timestamp.'.jpg';
    
            Storage::disk('s3')->put($pathThumb, $thumbEncoded);
    
            Log::info('Channel item image uploaded successfully to S3', ['path' => $path, 'thumb' => $pathThumb]);
    
            return [
                'media_url' => $path,
                'thumb' => $pathThumb
            ];
        } catch (\Exception $e) {
            Log::error('Error uploading channel item image to S3', ['error' => $e->getMessage()]);
            return ['error' => $e->getMessage()];
        }
    }

    public function uploadChannelImage($image, string $folder, string $type): string
    {
        try {
            $path = env('ASSETS_FOLDER').'/channel-'.$folder.'/'.$type.'-'.now()->timestamp.'.jpg';

            Storage::disk('s3')->put($path, file_get_contents($image->getRealPath()));

            Log::info('Imagem enviada com sucesso para o S3', ['path' => $path]);
            return $path;
        } catch (\Exception $e) {
            Log::error('Erro no upload para S3', ['erro' => $e->getMessage()]);
            return '';
        }
    }
}
