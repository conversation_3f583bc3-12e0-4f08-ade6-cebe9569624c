<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class HaveEventSelected
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if (!cache()->has('event')) {
            return redirect()->route('dashboard')->with('error', 'Selecione um evento por favor.');
        }
        return $next($request);
    }
}
