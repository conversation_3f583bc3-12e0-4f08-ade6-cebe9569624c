<?php

namespace App\Http\Controllers;

use App\Jobs\QueueFilesToDigitalOceanJob;
use App\Models\Event;
use App\Models\Channel;
use App\Models\ChannelItem;
use App\Traits\ImageTrait;
use Illuminate\Support\Arr;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;

class ChannelController extends Controller
{
    use ImageTrait;
    public function __construct()
    {
        $this->storageDisk = 'digitalocean';
    }
    public function index()
    {
        $channels = Channel::orderBy('title')
            ->where('swap_event_id', eventInfo()->event_id)
            ->get();

        $eventDate = Event::where('swap_event_id', eventInfo()->event_id)->first();
        return view('channel.index', compact('channels', 'eventDate'));
    }

    public function create()
    {
        return view('channel.create');
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'title' => 'required|min:5',
            'swap_event_id' => 'required'
        ]);

        $event = Event::where('swap_event_id', eventInfo()->event_id)->first();
        $channel = new Channel();
        $channel->title = $validatedData['title'];
        $channel->swap_event_id = $validatedData['swap_event_id'];
        $channel->event_id = $event->id;
        $channel->top_banner = '';
        $channel->background = '';
        $channel->save();

        if ($request->file('top_banner')) {
            $channel->top_banner = $this->uploadChannelImage($request->file('top_banner'), $channel->id, 'top-banner');
        }

        if ($request->file('background')) {
            $channel->background = $this->uploadChannelImage($request->file('background'), $channel->id, 'background');
        }

        if ($request->file('background') || $request->file('top_banner')) {
            $channel->save();
        }

        return redirect()->route('channel.index')->with('success', 'Canal cadastrado com sucesso!');
    }

    public function edit(Channel $channel)
    {
        return view('channel.edit', compact('channel'));
    }

    public function update(Channel $channel, Request $request)
    {
        $validatedData = $request->validate(['title' => 'required|min:5']);
        if ($request->file('top_banner')) {
            if (Storage::exists($channel->topBannerPath())) {
                Storage::delete($channel->topBannerPath());
            }
            $validatedData['top_banner'] = $this->uploadChannelImage($request->file('top_banner'), $channel->id, 'top-banner');
        }
        if ($request->file('background')) {
            if (Storage::exists($channel->backgroundPath())) {
                Storage::delete($channel->backgroundPath());
            }
            $validatedData['background'] = $this->uploadChannelImage($request->file('background'), $channel->id, 'background');
        }

        $channel->update($validatedData);

        return redirect()->route('channel.index')->with('success', 'Canal atualizado com sucesso!');
    }

    public function delete(Channel $channel)
    {
        $channel->delete();
        return redirect()->route('channel.index')->with('success', 'Canal removido com sucesso!');
    }

    public function itemIndex(Channel $channel)
    {
        if (eventInfo()->event_id != $channel->swap_event_id) {
            return redirect()->back()->with('error', 'Este canal não é do evento atual');
        }
        $channel->load('items');
        return view('channel.item.index', compact('channel'));
    }

    public function itemCreate(Channel $channel)
    {
        $places = $this->getPlaces();

        $itemTypes = ['agenda', 'banner'];
        return view('channel.item.create', compact('channel', 'itemTypes', 'places'));
    }

    public function itemStore(Channel $channel, Request $request)
    {
        $validatedData = $request->validate([
            'type' => 'required',
            'time' => 'required|numeric',
            'swap_place_id' => 'nullable',
            'swap_place_name' => 'nullable'
        ]);

        $validatedData['media_url'] = null;
        $validatedData['thumb'] = null;

        if ($request->file('media_file')) {
            $fileImage = $this->uploadChannelItemImage($request->file('media_file'), $channel->id);
            $validatedData['media_url'] = $fileImage['media_url'] ?? null;
            $validatedData['thumb'] = $fileImage['thumb'] ?? null;
        }

        $channel->items()->create($validatedData);

        return redirect()->route('channel.item.index', $channel->id)->with('success', 'Conteúdo cadastrado com sucesso!');
    }

    public function itemEdit(Channel $channel, ChannelItem $item)
    {
        $itemTypes = ['agenda', 'banner'];
        $places = $this->getPlaces();
        return view('channel.item.edit', compact('channel', 'item', 'places', 'itemTypes'));
    }

    public function itemUpdate(Channel $channel, ChannelItem $item, Request $request)
    {
        $validatedData = $request->validate([
            'type' => 'required',
            'time' => 'required|numeric',
            'swap_place_id' => 'nullable',
            'swap_place_name' => 'nullable'
        ]);

        if ($request->file('media_file')) {
            if (Storage::exists($item->mediaPath())) {
                Storage::delete($item->mediaPath());
            }

            if (Storage::exists($item->thumbPath())) {
                Storage::delete($item->thumbPath());
            }

            $fileImage = $this->uploadChannelItemImage($request->file('media_file'), $channel->id);
            $validatedData['media_url'] = $fileImage['media_url'] ?? null;
            $validatedData['thumb'] = $fileImage['thumb'] ?? null;
        }

        $item->update($validatedData);
        return redirect()->route('channel.item.index', $channel->id)->with('success', 'Item atualizado com sucesso!');
    }

    public function itemDelete(Channel $channel, ChannelItem $item)
    {
        $item->delete();
        return redirect()->route('channel.item.index', $channel->id)->with('success', 'Item removido com sucesso!');
    }

    protected function getPlaces()
    {
        return cache()->remember('event_places_'.eventInfo()->id, 5*60, function () {
            $response = Http::withHeaders([
                'Authorization' => config('services.swapcard.key'),
                'Content-Type' => 'application/json'
            ])->post(
                config('services.swapcard.uri'),
                [
                'query' => 'query EventById {
                    event(id:"'.eventInfo()->event_id.'"){
                        title
                        beginsAt
                        endsAt
                        totalPlannings
                        planningsPlaces {
                            id
                            name
                        }
                    }
                }']
            );

            $allPlaces = $response->json()['data']['event']['planningsPlaces'];
            return array_values(Arr::sort($allPlaces, function ($value) {
                return $value['name'];
            }));
        });
    }

    public function updateOrder(Channel $channel, Request $request)
    {
        foreach ($request->items as $key => $value) {
            ChannelItem::where('id', $value)->update(['order' => $key]);
        }
        return response()->json(['success' => true]);
    }

    public function moveFilesToDigitalOcean()
    {
        QueueFilesToDigitalOceanJob::dispatch()->onQueue('default');
        return "Done";
    }
}
