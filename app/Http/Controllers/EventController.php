<?php

namespace App\Http\Controllers;

use App\Models\Event;
use Illuminate\Http\Request;

class EventController extends Controller
{
    public function storeOrUpdate(Request $request)
    {
        $validatedData = $request->validate([
            'start_date' => 'date|required',
            'end_date' => 'date|required'
        ]);
        $event = Event::where('swap_event_id', eventInfo()->event_id)->first();
        if ($event) {
            $event->update($validatedData);
        } else {
            Event::create([...$validatedData, 'swap_event_id' => eventInfo()->event_id]);
        }
        return redirect()->back()->with('success', 'Data do congresso atualizada com sucesso');
    }
}
