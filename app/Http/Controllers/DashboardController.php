<?php

namespace App\Http\Controllers;

use App\Jobs\GetSwapcardEventsJob;
use App\Models\SwapcardEvents;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class DashboardController extends Controller
{
    public function __construct()
    {
    }

    public function index()
    {
        // Primeiro obtém eventos live em ordem alfabética (por título)
        $liveEvents = SwapcardEvents::where('is_live', true)
            ->orderBy('begins_at', 'asc')
            ->get();

        // Depois obtém eventos offline em ordem alfabética
        $offlineEvents = SwapcardEvents::where('is_live', false)
            ->orderBy('begins_at', 'asc')
            ->get();

        // Combina as duas coleções
        $allEvents = $liveEvents->merge($offlineEvents);

        // Pagina a coleção combinada
        $events = paginateCollection($allEvents, 10);

        return view('dashboard', compact('events'));
    }

    public function selectEvent(SwapcardEvents $event)
    {
        cache()->forget('event');
        cache()->rememberForever('event', function () use ($event) {
            return $event;
        });
        GetSwapcardEventsJob::dispatch()->onQueue('default');
        return redirect()->back()->with('success', 'Evento selecionado com sucesso!');
    }
}
