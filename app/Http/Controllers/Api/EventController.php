<?php

namespace App\Http\Controllers\Api;

use App\Models\Channel;
use App\Models\ChannelItem;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Models\SwapcardEvents;
use App\Services\SwapcardService;
use Illuminate\Support\Carbon;

class EventController extends Controller
{
    public function getEventList()
    {
        $events = SwapcardEvents::orderBy('begins_at', 'asc')
            ->select('id', 'event_id', 'title', 'begins_at as beginsAt', 'banner_url as imageUrl', 'is_live as isLive')
            ->get()
            ->toArray();

        return response()->json($events, 200);
    }

    public function getEventChannels($eventId)
    {
        try {
            // $swapEvent = SwapcardEvents::find($eventId);
            $eventChannels = Channel::where('swap_event_id', $eventId)
                ->orderBy('title')
                ->select('id', 'title')
                ->get();
            return response()->json($eventChannels, 200);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Event not found'
            ], 404);
        }
    }

    public function getChannelContent($channelId, $swapPlaceId = null)
    {
        // Cache key without timestamp to allow proper caching
        $cacheKey = 'channel_content_'.$channelId.'_'.($swapPlaceId ?? 'all');
        
        $channelContent = Cache::remember($cacheKey, 2*60, function() use ($channelId, $swapPlaceId) {
            // Log inicial para debug
            Log::info('Starting getChannelContent', [
                'channelId' => $channelId,
                'swapPlaceId' => $swapPlaceId
            ]);

            // Primeiro, buscamos o canal
            $channel = Channel::with('event')->find($channelId);

            if (!$channel) {
                Log::info('Channel not found', ['channelId' => $channelId]);
                return null;
            }

            // Depois, buscamos os itens separadamente
            $itemsQuery = ChannelItem::where('channel_id', $channelId)
                ->orderBy('order');

            if ($swapPlaceId) {
                $itemsQuery->where('swap_place_id', $swapPlaceId);
            }

            // Log da query SQL
            Log::info('SQL Query', [
                'sql' => $itemsQuery->toSql(),
                'bindings' => $itemsQuery->getBindings()
            ]);

            $items = $itemsQuery->get();

            // Log detalhado dos itens
            Log::info('Channel items before processing', [
                'total_items' => $items->count(),
                'items' => $items->map(function($item) {
                    return [
                        'id' => $item->id,
                        'swap_place_id' => $item->swap_place_id,
                        'type' => $item->type
                    ];
                })->toArray()
            ]);

            $channel = $channel->toArray();
            $channel['items'] = $items->map(function($item, $key) {
                return [...$item->toArray(), 'order' => $key];
            })->toArray();

            // Log final
            Log::info('Channel items after processing', [
                'total_items' => count($channel['items']),
                'filtered_items' => $channel['items']
            ]);

            return $channel;
        });

        if (!$channelContent) {
            return response()->json(['message' => 'Channel not found'], 404);
        }

        return response()->json($channelContent, 200);
    }

    public function getAgendaContentByDate($channelId, $date, $swapPlaceId = null)
    {
        $items = ChannelItem::where('channel_id', $channelId)
            ->where('type', 'agenda')
            ->with('channel');

        if ($swapPlaceId) {
            $items->where('swap_place_id', $swapPlaceId);
        }

        $items = $items->get()->unique('swap_place_id');
    
        if ($items->isEmpty()) {
            return response()->json(['message' => 'No agenda items found for this channel'], 404);
        }
    
        $allPlannings = collect();
    
        foreach ($items as $item) {
            if (!$item->channel || !$item->channel->swap_event_id || !$item->swap_place_id) {
                continue;
            }
    
            try {
                $swapcardService = new SwapcardService();
                $plannings = $swapcardService->getPlanningsByPlace(
                    $item->channel->swap_event_id,
                    $item->swap_place_id
                );
    
                $allPlannings = $allPlannings->merge($plannings);
            } catch (\Exception $e) {
                Log::error("Swapcard API error (channel item ID {$item->id}): " . $e->getMessage());
            }
        }
    
        if ($allPlannings->isEmpty()) {
            return response()->json(['message' => 'No plannings found'], 404);
        }
    
        $groupedByDate = $allPlannings->groupBy(function ($planning) {
            return \Carbon\Carbon::parse($planning['beginsAt'])->toDateString();
        });
    
        if (!$groupedByDate->has($date)) {
            return response()->json(['message' => 'No content for this date'], 404);
        }
    
        return response()->json($groupedByDate[$date]->values(), 200);
    }

    public function getPlanningsByPlace($channelId, $swapPlaceId)
    {
        $item = ChannelItem::where('channel_id', $channelId)
            ->where('type', 'agenda')
            ->where('swap_place_id', $swapPlaceId)
            ->with('channel')
            ->first();

        if (!$item) {
            return response()->json(['message' => 'No agenda item found for this place'], 404);
        }

        if (!$item->channel || !$item->channel->swap_event_id) {
            return response()->json(['message' => 'Invalid channel or event configuration'], 404);
        }

        try {
            $swapcardService = new SwapcardService();
            $plannings = $swapcardService->getPlanningsByPlace(
                $item->channel->swap_event_id,
                $swapPlaceId
            );

            if (empty($plannings)) {
                return response()->json(['message' => 'No plannings found for this place'], 404);
            }

            // Se uma data foi fornecida via query parameter, filtra os plannings
            if (request()->has('date')) {
                $date = request()->get('date');
                $plannings = collect($plannings)->filter(function ($planning) use ($date) {
                    return Carbon::parse($planning['beginsAt'])->toDateString() === $date;
                })->values()->all();

                if (empty($plannings)) {
                    return response()->json(['message' => 'No plannings found for this date'], 404);
                }
            }

            return response()->json($plannings, 200);
        } catch (\Exception $e) {
            Log::error("Swapcard API error (channel item ID {$item->id}): " . $e->getMessage());
            return response()->json(['message' => 'Error fetching plannings from Swapcard'], 500);
        }
    }
}