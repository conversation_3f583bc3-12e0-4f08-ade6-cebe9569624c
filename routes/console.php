<?php

use App\Jobs\GetSwapcardEventsJob;
use App\Services\SwapcardService;
use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;

/*
|--------------------------------------------------------------------------
| Console Routes
|--------------------------------------------------------------------------
|
| This file is where you may define all of your Closure based console
| commands. Each Closure is bound to a command instance allowing a
| simple approach to interacting with each command's IO methods.
|
*/

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

Artisan::command('swapcard:sync-events', function () {
    $this->comment('Syncing Swapcard events...');
    $swapcardService = new SwapcardService();
    $result = $swapcardService->syncEvents();
    $this->info('Total events: ' . $result['total_events']);
    $this->info('Total synced: ' . $result['total_synced']);
    $this->comment('Swapcard events synced successfully');
})->purpose('Sync Swapcard events directly without using queue');

Artisan::command('swapcard:list-events', function () {
    $this->comment('Listing Swapcard events...');
    $swapcardService = new SwapcardService();
    $events = $swapcardService->getEvents();

    $headers = ['ID', 'Is Live', 'Title', 'Begins At', 'Ends At'];
    $rows = [];

    foreach ($events as $event) {
        $rows[] = [
            $event['id'],
            $event['isLive'] ? '<fg=green>Yes</>' : '<fg=red>No</>',
            $event['title'],
            $event['beginsAt'],
            $event['endsAt']
        ];
    }

    $this->info('Total events: ' . count($events));
    $this->table($headers, $rows);
})->purpose('List all Swapcard events without saving them');
