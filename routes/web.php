<?php

use App\Models\User;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\EventController;
use App\Http\Controllers\ChannelController;
use App\Http\Controllers\InfoUserController;
use App\Http\Controllers\SessionsController;
use App\Http\Controllers\DashboardController;

Route::group(['middleware' => 'auth'], function () {
    Route::get('/', [HomeController::class, 'home']);
    Route::get('move-files-to-digital-ocean', [ChannelController::class, 'moveFilesToDigitalOcean']);
    Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('select-event/{event}', [DashboardController::class, 'selectEvent'])->name('select.event');
    Route::middleware(['auth', 'have.event'])->group(function () {
        Route::prefix('channel')->name('channel.')->group(function () {
            Route::get('/', [ChannelController::class, 'index'])->name('index');
            Route::get('create', [ChannelController::class, 'create'])->name('create');
            Route::post('store', [ChannelController::class, 'store'])->name('store');
            Route::get('edit/{channel}', [ChannelController::class, 'edit'])->name('edit');
            Route::put('update/{channel}', [ChannelController::class, 'update'])->name('update');
            Route::get('delete/{channel}', [ChannelController::class, 'delete'])->name('delete');
            Route::prefix('item/{channel}')->name('item.')->group(function () {
                Route::get('/', [ChannelController::class, 'itemIndex'])->name('index');
                Route::get('create', [ChannelController::class, 'itemCreate'])->name('create');
                Route::post('store', [ChannelController::class, 'itemStore'])->name('store');
                Route::get('edit/{item}', [ChannelController::class, 'itemEdit'])->name('edit');
                Route::put('update/{item}', [ChannelController::class, 'itemUpdate'])->name('update');
                Route::get('delete/{item}', [ChannelController::class, 'itemDelete'])->name('delete');
                Route::post('update-order', [ChannelController::class, 'updateOrder']);
            });
        });
        Route::post('concress-date-create-or-update', [EventController::class, 'storeOrUpdate'])->name('congress.date.update.store');
    });

    Route::get('/tokens/create', function () {
        $token = User::find(Auth()->user()->id)->createToken('Token Express '.rand(111, 999), ['agenda:api']);
        return ['token' => $token->plainTextToken];
    });

    Route::get('profile', function () {
        return view('profile');
    })->name('profile');


    Route::get('user-management', function () {
        return view('laravel-examples/user-management');
    })->name('user-management');

    Route::get('static-sign-in', function () {
        return view('static-sign-in');
    })->name('sign-in');

    Route::get('static-sign-up', function () {
        return view('static-sign-up');
    })->name('sign-up');

    Route::get('/logout', [SessionsController::class, 'destroy']);
    Route::get('/user-profile', [InfoUserController::class, 'create']);
    Route::post('/user-profile', [InfoUserController::class, 'store']);
    Route::get('/login', function () {
        return view('dashboard');
    })->name('sign-up');
});



Route::group(['middleware' => 'guest'], function () {
    // Route::get('/register', [RegisterController::class, 'create']);
    // Route::post('/register', [RegisterController::class, 'store']);
    Route::get('/login', [SessionsController::class, 'create']);
    Route::post('/session', [SessionsController::class, 'store']);
    // Route::get('/login/forgot-password', [ResetController::class, 'create']);
    // Route::post('/forgot-password', [ResetController::class, 'sendEmail']);
    // Route::get('/reset-password/{token}', [ResetController::class, 'resetPass'])->name('password.reset');
    // Route::post('/reset-password', [ChangePasswordController::class, 'changePassword'])->name('password.update');
});

Route::get('/login', function () {
    return view('session/login-session');
})->name('login');
