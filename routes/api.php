<?php

use App\Http\Controllers\Api\EventController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::prefix('event')->group(function () {
    Route::get('list', [EventController::class, 'getEventList']);
    Route::get('get-channels/{eventId}', [EventController::class, 'getEventChannels']);
    Route::get('get-channel-content/{channelId}/{swapPlaceId?}', [EventController::class, 'getChannelContent']);
    Route::get('get-agenda-content-by-date/{item}/{date}', [EventController::class, 'getAgendaContentByDate']);
    Route::get('get-plannings-by-place/{channelId}/{swapPlaceId}', [EventController::class, 'getPlanningsByPlace']);
});
