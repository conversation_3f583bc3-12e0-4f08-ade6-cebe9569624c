{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.3|^8.0", "fruitcake/laravel-cors": "^3.0", "guzzlehttp/guzzle": "^7.4", "intervention/image": "^3.11", "laravel/framework": "^9.0", "laravel/sanctum": "^2.15", "laravel/tinker": "^2.6", "league/flysystem-aws-s3-v3": "^3.5"}, "require-dev": {"spatie/laravel-ignition": "^1.0", "fakerphp/faker": "^1.17", "laravel/sail": "^1.12", "mockery/mockery": "^1.4.2", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10"}, "autoload": {"files": ["app/helpers.php"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}