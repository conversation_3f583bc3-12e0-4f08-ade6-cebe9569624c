<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('swapcard_sync_logs', function (Blueprint $table) {
            $table->id();
            $table->integer('total_events')->default(0);
            $table->integer('total_synced')->default(0);
            $table->boolean('status')->default(true);
            $table->text('error_message')->nullable();
            $table->string('source')->default('manual'); // manual, job, api
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('swapcard_sync_logs');
    }
};
