<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('swapcard_events', function (Blueprint $table) {
            $table->id();
            $table->string('event_id');
            $table->string('title');
            $table->string('banner_url')->nullable();
            $table->dateTime('begins_at')->nullable();
            $table->dateTime('ends_at')->nullable();
            $table->string('total_plannings')->nullable();
            $table->json('info')->nullable();
            $table->json('places')->nullable();
            $table->unique('event_id');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('swapcard_events');
    }
};
