<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <link rel="apple-touch-icon" sizes="76x76" href="https://demos.creative-tim.com/argon-design-system-pro/assets/img/apple-icon.png">
    <link rel="icon" href="https://demos.creative-tim.com/argon-design-system-pro/assets/img/apple-icon.png" type="image/png">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="author" content="Creative Tim">
    <title>
        Installation | Soft UI Dashboard Bootstrap @ Creative Tim
    </title>
    <link rel="canonical" href="https://www.creative-tim.com/learning-lab/bootstrap/installation/soft-ui-dashboard">
    <meta name="keywords" content="creative tim, updivision, html dashboard, laravel, html css dashboard laravel, soft ui dashboard laravel, laravel soft ui dashboard, soft ui admin, laravel dashboard, laravel admin, web dashboard, bootstrap 5 dashboard laravel, bootstrap 5, css3 dashboard, bootstrap 5 admin laravel, soft ui dashboard bootstrap 5 laravel, frontend, responsive bootstrap 5 dashboard, soft ui dashboard, soft ui laravel bootstrap 5 dashboard" />
<meta name="description" content="A free Laravel Dashboard featuring dozens of UI components & basic Laravel CRUDs." />
<meta itemprop="name" content="Soft UI Dashboard Laravel by Creative Tim & UPDIVISION" />
<meta itemprop="description" content="A free Laravel Dashboard featuring dozens of UI components & basic Laravel CRUDs." />
<meta itemprop="image" content="https://s3.amazonaws.com/creativetim_bucket/products/602/original/soft-ui-dashboard-laravel.jpg" />
<meta name="twitter:card" content="product" />
<meta name="twitter:site" content="@creativetim" />
<meta name="twitter:title" content="Soft UI Dashboard Laravel by Creative Tim & UPDIVISION" />
<meta name="twitter:description" content="A free Laravel Dashboard featuring dozens of UI components & basic Laravel CRUDs." />
<meta name="twitter:creator" content="@creativetim" />
<meta name="twitter:image" content="https://s3.amazonaws.com/creativetim_bucket/products/602/original/soft-ui-dashboard-laravel.jpg" />
<meta property="fb:app_id" content="655968634437471" />
<meta property="og:title" content="Soft UI Dashboard Laravel by Creative Tim & UPDIVISION" />
<meta property="og:type" content="article" />
<meta property="og:url" content="https://www.creative-tim.com/live/soft-ui-dashboard-laravel" />
<meta property="og:image" content="https://s3.amazonaws.com/creativetim_bucket/products/602/original/soft-ui-dashboard-laravel.jpg" />
<meta property="og:description" content="A free Laravel Dashboard featuring dozens of UI components & basic Laravel CRUDs." />
<meta property="og:site_name" content="Creative Tim" />

    <link rel="stylesheet" href="https://demos.creative-tim.com/argon-design-system-pro/assets/css/nucleo-icons.css" type="text/css">
    <link href="" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta1/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-giJF6kkoqNQ00vy+HMDP7azOuL0xtbfIcaT9wjKHr8RbDVddVHyTfAAsrekwKmP1" crossorigin="anonymous">
    <link rel="stylesheet" href="https://demos.creative-tim.com/test/soft-ui-dashboard-pro/assets/css/soft-ui-dashboard.min.css?v=1.0.0" type="text/css">
    <link rel="stylesheet" href="../../assets/demo.css" type="text/css">
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet">
    <link href="https://demos.creative-tim.com/argon-design-system-pro/assets/css/nucleo-icons.css" rel="stylesheet">
    <script async="" src="https://s.pinimg.com/ct/lib/main.6ae4a9fc.js"></script>
    <script type="text/javascript" async="" src="https://s.pinimg.com/ct/core.js"></script>
    <script type="text/javascript" async="" src="https://static.hotjar.com/c/hotjar-99526.js?sv=7"></script>
    <script type="text/javascript" async="" src="https://www.google-analytics.com/analytics.js"></script>
    <script type="text/javascript" async="" src="https://www.google-analytics.com/gtm/js?id=GTM-K9BGS8K&amp;cid=1113738810.1638876382&amp;aip=true"></script>
    <script src="https://connect.facebook.net/signals/config/111649226022273?v=2.9.48&amp;r=stable" async=""></script>
    <script async="" src="//connect.facebook.net/en_US/fbevents.js"></script>
    <script async="" src="https://www.googletagmanager.com/gtm.js?id=GTM-NKDMSK6"></script>
    <script async="" src="https://www.google-analytics.com/analytics.js"></script>
    <script src="https://kit.fontawesome.com/42d5adcbca.js" crossorigin="anonymous"></script>

    <!-- Anti-flicker snippet (recommended)  -->
    <style>
      .async-hide {
        opacity: 0 !important
      }
    </style>
    <script>
      (function(a, s, y, n, c, h, i, d, e) {
        s.className += ' ' + y;
        h.start = 1 * new Date;
        h.end = i = function() {
          s.className = s.className.replace(RegExp(' ?' + y), '')
        };
        (a[n] = a[n] || []).hide = h;
        setTimeout(function() {
          i();
          h.end = null
        }, c);
        h.timeout = c;
      })(window, document.documentElement, 'async-hide', 'dataLayer', 4000, {
        'GTM-K9BGS8K': true
      });
    </script>
    <!-- Analytics-Optimize Snippet -->
    <script>
      (function(i, s, o, g, r, a, m) {
        i['GoogleAnalyticsObject'] = r;
        i[r] = i[r] || function() {
          (i[r].q = i[r].q || []).push(arguments)
        }, i[r].l = 1 * new Date();
        a = s.createElement(o),
          m = s.getElementsByTagName(o)[0];
        a.async = 1;
        a.src = g;
        m.parentNode.insertBefore(a, m)
      })(window, document, 'script', 'https://www.google-analytics.com/analytics.js', 'ga');
      ga('create', 'UA-46172202-22', 'auto', {
        allowLinker: true
      });
      ga('set', 'anonymizeIp', true);
      ga('require', 'GTM-K9BGS8K');
      ga('require', 'displayfeatures');
      ga('require', 'linker');
      ga('linker:autoLink', ["2checkout.com", "avangate.com"]);
    </script>
    <!-- end Analytics-Optimize Snippet -->
    <!-- Google Tag Manager -->
    <script>
      (function(w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({
          'gtm.start': new Date().getTime(),
          event: 'gtm.js'
        });
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != 'dataLayer' ? '&l=' + l : '';
        j.async = true;
        j.src =
          'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
        f.parentNode.insertBefore(j, f);
      })(window, document, 'script', 'dataLayer', 'GTM-NKDMSK6');
    </script>
    <!-- End Google Tag Manager -->
    <!-- This is for docs typography and layout -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet">
    <link href="../../assets/docs-soft.css" rel="stylesheet">
  <script async="" src="https://script.hotjar.com/modules.54959b9c945092ba123f.js" charset="utf-8"></script>
  <style type="text/css">
  iframe#_hjRemoteVarsFrame {display: none !important; width: 1px !important; height: 1px !important; opacity: 0 !important; pointer-events: none !important;}</style>
  <meta http-equiv="origin-trial" content="A13s4hjGQNypqXJtC3txOObvdElWKqJttxI7WhcRiEX0+Y28BmRR2ZTW8rSV659YQd1xb9tpLof5Eehz3SMUXgwAAACHeyJvcmlnaW4iOiJodHRwczovL3d3dy5waW50ZXJlc3QuY29tOjQ0MyIsImZlYXR1cmUiOiJDb252ZXJzaW9uTWVhc3VyZW1lbnQiLCJleHBpcnkiOjE2MzQwODMxOTksImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9">
</head>

<body class="docs">
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe
            src="https://www.googletagmanager.com/ns.html?id=GTM-NKDMSK6"
            height="0"
            width="0"
            style="display: none; visibility: hidden"
        ></iframe
    ></noscript>
    <!-- End Google Tag Manager (noscript) -->
    <header class="ct-docs-navbar">
        <a class="ct-docs-navbar-brand" href="javascript:void(0)" aria-label="Bootstrap">
            </a><a href="https://www.creative-tim.com/" class="ct-docs-navbar-text" target="_blank">
                Creative Tim
            </a>
            <div class="ct-docs-navbar-border"></div>
            <a href="../../documentation/getting-started/installation.html" class="ct-docs-navbar-text">
                Docs
            </a>

        <ul class="ct-docs-navbar-nav-left">
            <li class="ct-docs-nav-item-dropdown">
                <a href="javascript:;" class="ct-docs-navbar-nav-link" role="button">
                    <span class="ct-docs-navbar-nav-link-inner--text">Live Preview</span>
                </a>
                <div class="ct-docs-navbar-dropdown-menu" aria-labelledby="DropdownPreview">
                    <a class="ct-docs-navbar-dropdown-item" href="https://soft-ui-dashboard-laravel.creative-tim.com/" target="_blank">
                        Soft UI Dashboard
                    </a>
                </div>
            </li>
            <li class="ct-docs-nav-item-dropdown">
                <a href="javascript:;" class="ct-docs-navbar-nav-link" role="button">
                    <span class="ct-docs-navbar-nav-link-inner--text">Support</span>
                </a>
                <div class="ct-docs-navbar-dropdown-menu" aria-labelledby="DropdownSupport">
                    <a class="ct-docs-navbar-dropdown-item" href="https://github.com/creativetimofficial/soft-ui-dashboard-laravel/issues" target="_blank">
                        Soft UI Dashboard
                    </a>
                </div>
            </li>
        </ul>
        <ul class="ct-docs-navbar-nav-right">
            <li class="ct-docs-navbar-nav-item">
                <a class="ct-docs-navbar-nav-link" href="https://www.creative-tim.com/product/soft-ui-dashboard-pro-laravel" target="_blank">Buy Now</a>
            </li>
            <li class="ct-docs-navbar-nav-item">
                <a class="ct-docs-navbar-nav-link" href="https://www.creative-tim.com/product/soft-ui-dashboard-laravel" target="_blank">Download Free</a>
            </li>
        </ul>
        <button class="ct-docs-navbar-toggler" type="button">
            <span class="ct-docs-navbar-toggler-icon"></span>
        </button>
    </header>
    <div class="ct-docs-main-container">
        <div class="ct-docs-main-content-row">
            <div class="ct-docs-sidebar-col">
                <nav class="ct-docs-sidebar-collapse-links">
                    <div class="ct-docs-sidebar-product">
                        <div class="ct-docs-sidebar-product-image">
                            <img src="../../assets/img/bootstrap-5.svg">
                        </div>
                        <p class="ct-docs-sidebar-product-text">
                            Soft UI Dashboard
                        </p>
                    </div>
                    <div class="ct-docs-toc-item-active">
                        <a class="ct-docs-toc-link" href="javascript:void(0)">
                            <div class="d-inline-block">
                                <div class="
                                        icon icon-xs
                                        border-radius-md
                                        bg-gradient-warning
                                        text-center
                                        mr-2
                                        d-flex
                                        align-items-center
                                        justify-content-center
                                        me-1
                                    ">
                                    <i class="ni ni-active-40 text-white"></i>
                                </div>
                            </div>
                            Getting started
                        </a>
                        <ul class="ct-docs-nav-sidenav ms-4 ps-1">
                            <li class="">
                                <a href="../../documentation/getting-started/overview.html">
                                    Overview
                                </a>
                            </li>
                            <li class="">
                                <a href="../../documentation/getting-started/license.html">
                                    License
                                </a>
                            </li>
                            <li class="ct-docs-nav-sidenav-active">
                                <a href="../../documentation/getting-started/installation.html">
                                    Installation
                                </a>
                            </li>
                            <li class="">
                                <a href="../../documentation/getting-started/build-tools.html">
                                    Build Tools
                                </a>
                            </li>
                            <li class="">
                                <a href="../../documentation/getting-started/bootstrap.html">
                                    What is Bootstrap
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="ct-docs-toc-item-active">
                        <a class="ct-docs-toc-link" href="javascript:void(0)">
                            <div class="d-inline-block">
                                <div class="
                                        icon icon-xs
                                        border-radius-md
                                        bg-gradient-warning
                                        text-center
                                        mr-2
                                        d-flex
                                        align-items-center
                                        justify-content-center
                                        me-1
                                    ">
                                    <i class="ni ni-folder-17 text-white"></i>
                                </div>
                            </div>
                            Laravel
                        </a>
                        <ul class="ct-docs-nav-sidenav ms-4 ps-1">
                            <li class="">
                                <a href="../../documentation/laravel/login.html">
                                    Login
                                </a>
                            </li>
                            <li class="">
                                <a href="../../documentation/laravel/sign-up.html">
                                    Sign Up
                                </a>
                            </li>
                            <li class="">
                                <a href="../../documentation/laravel/forgot-password.html">
                                    Forgot Password
                                </a>
                            </li>
                            <li class="">
                                <a href="../../documentation/laravel/reset-password.html">
                                    Reset Password
                                </a>
                            </li>
                            <li class="">
                                <a href="../../documentation/laravel/user-profile.html">
                                User Profile
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="ct-docs-toc-item-active">
                        <a class="ct-docs-toc-link" href="javascript:void(0)">
                            <div class="d-inline-block">
                                <div class="
                                        icon icon-xs
                                        border-radius-md
                                        bg-gradient-warning
                                        text-center
                                        mr-2
                                        d-flex
                                        align-items-center
                                        justify-content-center
                                        me-1
                                    ">
                                    <i class="ni ni-folder-17 text-white"></i>
                                </div>
                            </div>
                            Foundation
                        </a>
                        <ul class="ct-docs-nav-sidenav ms-4 ps-1">
                            <li class="">
                                <a href="../../documentation/foundation/colors.html">
                                    Colors
                                </a>
                            </li>
                            <li class="">
                                <a href="../../documentation/foundation/grid.html">
                                    Grid
                                </a>
                            </li>
                            <li class="">
                                <a href="../../documentation/foundation/typography.html">
                                    Typography
                                </a>
                            </li>
                            <li class="">
                                <a href="../../documentation/foundation/icons.html">
                                    Icons
                                </a>
                            </li>
                            <li class="">
                                <a href="../../documentation/foundation/utilities.html">
                                    Utilities
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="ct-docs-toc-item-active">
                        <a class="ct-docs-toc-link" href="javascript:void(0)">
                            <div class="d-inline-block">
                                <div class="
                                        icon icon-xs
                                        border-radius-md
                                        bg-gradient-warning
                                        text-center
                                        mr-2
                                        d-flex
                                        align-items-center
                                        justify-content-center
                                        me-1
                                    ">
                                    <i class="ni ni-app text-white"></i>
                                </div>
                            </div>
                            Components
                        </a>
                        <ul class="ct-docs-nav-sidenav ms-4 ps-1">
                            <li class="">
                                <a href="../../documentation/components/alerts.html">
                                    Alerts
                                </a>
                            </li>
                            <li class="">
                                <a href="../../documentation/components/badge.html">
                                    Badge
                                </a>
                            </li>
                            <li class="">
                                <a href="../../documentation/components/buttons.html">
                                    Buttons
                                </a>
                            </li>
                            <li class="">
                                <a href="../../documentation/components/social-buttons.html">
                                    Social Buttons
                                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                                </a>
                            </li>
                            <li class="">
                                <a href="../../documentation/components/cards.html">
                                    Cards
                                </a>
                            </li>
                            <li class="">
                                <a href="../../documentation/components/carousel.html">
                                    Carousel
                                </a>
                            </li>
                            <li class="">
                                <a href="../../documentation/components/collapse.html">
                                    Collapse
                                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                                </a>
                            </li>
                            <li class="">
                                <a href="../../documentation/components/dropdowns.html">
                                    Dropdowns
                                </a>
                            </li>
                            <li class="">
                                <a href="../../documentation/components/forms.html">
                                    Forms
                                </a>
                            </li>
                            <li class="">
                                <a href="../../documentation/components/input-group.html">
                                    Input Group
                                </a>
                            </li>
                            <li class="">
                                <a href="../../documentation/components/list-group.html">
                                    List Group
                                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                                </a>
                            </li>
                            <li class="">
                                <a href="../../documentation/components/modal.html">
                                    Modal
                                </a>
                            </li>
                            <li class="">
                                <a href="../../documentation/components/navs.html">
                                    Navs
                                </a>
                            </li>
                            <li class="">
                                <a href="../../documentation/components/navbar.html">
                                    Navbar
                                </a>
                            </li>
                            <li class="">
                                <a href="../../documentation/components/pagination.html">
                                    Pagination
                                </a>
                            </li>
                            <li class="">
                                <a href="../../documentation/components/popovers.html">
                                    Popovers
                                </a>
                            </li>
                            <li class="">
                                <a href="../../documentation/components/progress.html">
                                    Progress
                                </a>
                            </li>
                            <li class="">
                                <a href="../../documentation/components/spinners.html">
                                    Spinners
                                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                                </a>
                            </li>
                            <li class="">
                                <a href="../../documentation/components/tables.html">
                                    Tables
                                </a>
                            </li>
                            <li class="">
                                <a href="../../documentation/components/tooltips.html">
                                    Tooltips
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="ct-docs-toc-item-active">
                        <a class="ct-docs-toc-link" href="javascript:void(0)">
                            <div class="d-inline-block">
                                <div class="
                                        icon icon-xs
                                        border-radius-md
                                        bg-gradient-warning
                                        text-center
                                        mr-2
                                        d-flex
                                        align-items-center
                                        justify-content-center
                                        me-1
                                    ">
                                    <i class="ni ni-settings text-white"></i>
                                </div>
                            </div>
                            Plugins
                        </a>
                        <ul class="ct-docs-nav-sidenav ms-4 ps-1">
                        <li class="">
                            <a href="../../documentation/plugins/countUpJs.html">
                            CountUp JS
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                            </a>
                        </li>
                        <li class="">
                            <a href="../../documentation/plugins/charts.html">
                            Charts
                            </a>
                        </li>
                        <li class="">
                            <a href="../../documentation/plugins/datepicker.html">
                            Datepicker
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                            </a>
                        </li>
                        <li class="">
                            <a href="../../documentation/plugins/fullcalendar.html">
                            Fullcalendar
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                            </a>
                        </li>
                        <li class="">
                            <a href="../../documentation/plugins/sliders.html">
                            Sliders
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                            </a>
                        </li>
                        <li class="">
                            <a href="../../documentation/plugins/choices.html">
                            Choices
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                            </a>
                        </li>
                        <li class="">
                            <a href="../../documentation/plugins/dropzone.html">
                            Dropzone
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                            </a>
                        </li>
                        <li class="">
                            <a href="../../documentation/plugins/datatables.html">
                            Datatables
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                            </a>
                        </li>
                        <li class="">
                            <a href="../../documentation/plugins/kanban.html">
                            Kanban
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                            </a>
                        </li>
                        <li class="">
                            <a href="../../documentation/plugins/photo-swipe.html">
                            Photo Swipe
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                            </a>
                        </li>
                        <li class="">
                            <a href="../../documentation/plugins/quill.html">
                            Quill
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                            </a>
                        </li>
                        <li class="">
                            <a href="../../documentation/plugins/sweet-alerts.html">
                            Sweet Alerts
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                            </a>
                        </li>
                        <li class="">
                            <a href="../../documentation/plugins/threeJs.html">
                              Three JS
                              <span class="ct-docs-sidenav-pro-badge">Pro</span>
                            </a>
                        </li>
                        <li class="">
                            <a href="../../documentation/plugins/wizard.html">
                            Wizard
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                            </a>
                        </li>
                        </ul>
                    </div>
                </nav>
            </div>
            <div class="ct-docs-toc-col">
                <ul class="section-nav">
                    <li class="toc-entry toc-h2">
                        <a href="#prerequisites">Prerequisites</a>
                    </li>
                    <li class="toc-entry toc-h2">
                        <a href="#installation">Installation</a>
                    </li>
                    <li class="toc-entry toc-h2">
                        <a href="#usage">Usage</a>
                    </li>
                    <li class="toc-entry toc-h2"><a href="#tooling-setup">Tooling setup</a></li>
                    <li class="toc-entry toc-h2"><a href="#bootstrap-cdn">Bootstrap CDN</a></li>
                    <li class="toc-entry toc-h2"><a href="#css">CSS</a></li>
                    <li class="toc-entry toc-h2"><a href="#js">JS</a></li>
                    <li class="toc-entry toc-h2"><a href="#bootstrap-starter-template">Bootstrap starter template</a>
                    </li>
                    <li class="toc-entry toc-h2"><a href="#important-globals">Important globals</a>
                        <ul>
                            <li class="toc-entry toc-h3"><a href="#html5-doctype">HTML5 doctype</a></li>
                            <li class="toc-entry toc-h3"><a href="#responsive-meta-tag">Responsive meta tag</a></li>
                            <li class="toc-entry toc-h3"><a href="#bootstrap-components">Bootstrap components</a></li>
                            <li class="toc-entry toc-h3"><a href="#bootstrap-tutorial">Bootstrap tutorial</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
            <main class="ct-docs-content-col" role="main">
                <div class="ct-docs-page-title">
                    <h1 class="ct-docs-page-h1-title" id="content">
                        Installation
                    </h1>
                    <div class="avatar-group mt-3">
                    </div>
                </div>
                <p class="ct-docs-page-title-lead">To start using this dashboard you will need to import some files in
                    your current project or start from scratch using our template (scroll down in this page to view it).
                </p>
                <hr class="ct-docs-hr">
                <h2 id="prerequisites">Prerequisites</h2>
                <p>
                    If you don't already have an Apache local environment
                    with PHP and MySQL, use one of the following links:
                </p>
                <ul>
                    <li>
                        Windows
                        <a
                            href="https://updivision.com/blog/post/beginner-s-guide-to-setting-up-your-local-development-environment-on-windows">https://updivision.com/blog/post/beginner-s-guide-to-setting-up-your-local-development-environment-on-windows</a>
                    </li>
                    <li>
                        Linux
                        <a
                            href="https://howtoubuntu.org/how-to-install-lamp-on-ubuntu">https://howtoubuntu.org/how-to-install-lamp-on-ubuntu</a>
                    </li>
                    <li>
                        Mac
                        <a href=" https://wpshout.com/quick-guides/how-to-install-mamp-on-your-mac/">
                            https://wpshout.com/quick-guides/how-to-install-mamp-on-your-mac/</a>
                    </li>
                </ul>
                <p>
                    Also, you will need to install Composer:
                    <a href="https://getcomposer.org/doc/00-intro.md">https://getcomposer.org/doc/00-intro.md</a>
                </p>
                <p>
                    And Laravel:
                    <a href="https://laravel.com/docs/9.x/installation">https://laravel.com/docs/9.x/installation</a>
                </p>

                <h2 id="installation">Installation</h2>
                <ol>
                    <li>Unzip the downloaded archive</li>
                    <li>
                        Copy and paste <b>SoftUI-dashboard-Laravel-free</b> folder in your <b>projects</b> folder.
                        Rename the folder to your project's name
                    </li>
                    <li>
                        In your terminal run
                        <code class="language-html">composer install</code>
                    </li>
                    <li>
                        Copy
                        <code class="language-html">.env.example</code> to
                        <code class="language-html">.env</code> and updated
                        the configurations (mainly the database
                        configuration)
                    </li>
                    <li>
                        In your terminal run
                        <code class="language-html">php artisan key:generate</code>
                    </li>
                    <li>
                        Run
                        <code class="language-html">php artisan migrate --seed</code>
                        to create the database tables and seed the roles and
                        users tables
                    </li>
                    <li>
                        Run
                        <code class="language-html">php artisan storage:link
                        </code>
                        to create the storage symlink (if you are using
                        <b>Vagrant</b> with <b>Homestead</b> for
                        development, remember to ssh into your virtual
                        machine and run the command from there).
                    </li>
                </ol>
                <h2 id="usage">Usage</h2>
                <p>
                    Register an user or login with data from your database and start testing (make sure to run the
                    migrations and seeders for the credentials to be available).
                </p>
                <p>

                    Besides the dashboard, the auth pages, the billing and tables pages, it has also an edit profile
                    page. All the necessary files are installed out of the box and all the needed routes are added to
                    <code class="language-html">routes/web.php</code>. Keep in mind that all of the features can be
                    viewed once you login using the credentials provided or by registering your own user.
                </p>
                <h2 id="tooling-setup">Tooling setup</h2>
                <h2 id="bootstrap-cdn">Bootstrap CDN</h2>
                <p>Skip the download with <a href="https://www.bootstrapcdn.com/" target="_blank"
                        rel="nofollow">BootstrapCDN</a> to deliver cached version of Bootstrap’s compiled CSS and JS to
                    your project.</p>
                <figure class="highlight">
                    <pre><code class="language-html" data-lang="html"><span class="c">&lt;!-- CSS only --&gt;</span>
<span class="nt">&lt;link</span> <span class="na">href=</span><span class="s">"https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css"</span> <span class="na">rel=</span><span class="s">"stylesheet"</span> <span class="na">integrity=</span><span class="s">"sha384-wEmeIV1mKuiNpC+IOBjI7aAzPcEZeedi5yW5f2yOq55WWLwNGmvvx4Um1vskeMj0"</span> <span class="na">crossorigin=</span><span class="s">"anonymous"</span><span class="nt">&gt;</span>
<span class="c">&lt;!-- JavaScript Bundle with Popper --&gt;</span>
<span class="nt">&lt;script </span><span class="na">src=</span><span class="s">"https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"</span> <span class="na">integrity=</span><span class="s">"sha384-p34f1UUtsS3wqzfto5wAAmdvj+osOnFyQFpp4Ua3gs/ZVWx6oOypYoCJhGGScy+8"</span> <span class="na">crossorigin=</span><span class="s">"anonymous"</span><span class="nt">&gt;&lt;/script&gt;</span></code></pre>
                </figure>
                <h2 id="css">CSS</h2>
                <p>Copy-paste the stylesheet <code class="language-plaintext highlighter-rouge">&lt;link&gt;</code> into
                    your <code class="language-plaintext highlighter-rouge">&lt;head&gt;</code> before all other
                    stylesheets to load our CSS.</p>
                <figure class="highlight">
                    <pre><code class="language-html" data-lang="html"><span class="c">&lt;!-- Fonts --&gt;</span>
<span class="nt">&lt;link</span> <span class="na">href=</span><span class="s">"https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700"</span> <span class="na">rel=</span><span class="s">"stylesheet"</span><span class="nt">&gt;</span>

<span class="c">&lt;!-- Icons --&gt;</span>
<span class="nt">&lt;link</span> <span class="na">href=</span><span class="s">"../assets/css/nucleo-icons.css"</span> <span class="na">rel=</span><span class="s">"stylesheet"</span> <span class="nt">/&gt;</span>
<span class="nt">&lt;link</span> <span class="na">href=</span><span class="s">"../assets/css/nucleo-svg.css"</span> <span class="na">rel=</span><span class="s">"stylesheet"</span> <span class="nt">/&gt;</span>

<span class="c">&lt;!-- Font Awesome Icons --&gt;</span>
<span class="nt">&lt;script </span><span class="na">src=</span><span class="s">"https://kit.fontawesome.com/42d5adcbca.js"</span> <span class="na">crossorigin=</span><span class="s">"anonymous"</span><span class="nt">&gt;&lt;/script&gt;</span>

<span class="c">&lt;!-- CSS Files --&gt;</span>
<span class="nt">&lt;link</span> <span class="na">id=</span><span class="s">"pagestyle"</span> <span class="na">href=</span><span class="s">"../assets/css/soft-ui-dashboard.css"</span> <span class="na">rel=</span><span class="s">"stylesheet"</span> <span class="nt">/&gt;</span></code></pre>
                </figure>
                <h2 id="js">JS</h2>
                <p>Many of our components require the use of JavaScript to function. Specifically , <a
                        href="https://popper.js.org/" rel="nofollow">Popper.js</a>, and our own JavaScript plugins.
                    Place the following <code class="language-plaintext highlighter-rouge">&lt;script&gt;</code>s near
                    the end of your pages, right before the closing <code
                        class="language-plaintext highlighter-rouge">&lt;/body&gt;</code> tag, to enable them. Popper.js
                    must come and then our JavaScript plugins.</p>
                <figure class="highlight">
                    <pre><code class="language-html" data-lang="html"><span class="c">&lt;!-- Core --&gt;</span>
<span class="nt">&lt;script </span><span class="na">src=</span><span class="s">"../assets/js/core/popper.min.js"</span><span class="nt">&gt;&lt;/script&gt;</span>
<span class="nt">&lt;script </span><span class="na">src=</span><span class="s">"../assets/js/core/bootstrap.min.js"</span><span class="nt">&gt;&lt;/script&gt;</span>

<span class="c">&lt;!-- Theme JS --&gt;</span>
<span class="nt">&lt;script </span><span class="na">src=</span><span class="s">"../assets/js/soft-ui-dashboard.min.js"</span><span class="nt">&gt;&lt;/script&gt;</span></code></pre>
                </figure>
                <p>Need to use a certain plugin in your page? You can find out how to integrate them and make them work
                    in the Plugins dedicated page. In this way you will be sure that your website is optimized and uses
                    only the needed resources.</p>
                <h2 id="bootstrap-starter-template">Bootstrap starter template</h2>
                <p>Be sure to have your pages set up with the latest design and development standards. That means using
                    an HTML5 doctype and including a viewport meta tag for proper responsive behaviors. Put it all
                    together and your pages should look like this:</p>
                <figure class="highlight">
                    <pre><code class="language-html" data-lang="html"><span class="cp">&lt;!DOCTYPE html&gt;</span>
<span class="nt">&lt;html</span> <span class="na">lang=</span><span class="s">"en"</span><span class="nt">&gt;</span>

<span class="nt">&lt;head&gt;</span>
  <span class="nt">&lt;meta</span> <span class="na">charset=</span><span class="s">"utf-8"</span> <span class="nt">/&gt;</span>
  <span class="nt">&lt;meta</span> <span class="na">name=</span><span class="s">"viewport"</span> <span class="na">content=</span><span class="s">"width=device-width, initial-scale=1, shrink-to-fit=no"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;link</span> <span class="na">rel=</span><span class="s">"apple-touch-icon"</span> <span class="na">sizes=</span><span class="s">"76x76"</span> <span class="na">href=</span><span class="s">"../assets/img/apple-icon.png"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;link</span> <span class="na">rel=</span><span class="s">"icon"</span> <span class="na">type=</span><span class="s">"image/png"</span> <span class="na">href=</span><span class="s">"../assets/img/favicon.png"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;title&gt;</span>
  Soft UI Dashboard by Creative Tim
  <span class="nt">&lt;/title&gt;</span>
  <span class="c">&lt;!--     Fonts and icons     --&gt;</span>
  <span class="nt">&lt;link</span> <span class="na">href=</span><span class="s">"https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700"</span> <span class="na">rel=</span><span class="s">"stylesheet"</span> <span class="nt">/&gt;</span>
  <span class="c">&lt;!-- Nucleo Icons --&gt;</span>
  <span class="nt">&lt;link</span> <span class="na">href=</span><span class="s">"../assets/css/nucleo-icons.css"</span> <span class="na">rel=</span><span class="s">"stylesheet"</span> <span class="nt">/&gt;</span>
  <span class="nt">&lt;link</span> <span class="na">href=</span><span class="s">"../assets/css/nucleo-svg.css"</span> <span class="na">rel=</span><span class="s">"stylesheet"</span> <span class="nt">/&gt;</span>
  <span class="c">&lt;!-- Font Awesome Icons --&gt;</span>
  <span class="nt">&lt;script </span><span class="na">src=</span><span class="s">"https://kit.fontawesome.com/42d5adcbca.js"</span> <span class="na">crossorigin=</span><span class="s">"anonymous"</span><span class="nt">&gt;&lt;/script&gt;</span>
  <span class="c">&lt;!-- CSS Files --&gt;</span>
  <span class="nt">&lt;link</span> <span class="na">id=</span><span class="s">"pagestyle"</span> <span class="na">href=</span><span class="s">"../assets/css/soft-ui-dashboard.css"</span> <span class="na">rel=</span><span class="s">"stylesheet"</span> <span class="nt">/&gt;</span>
<span class="nt">&lt;/head&gt;</span>

<span class="nt">&lt;body</span> <span class="na">class=</span><span class="s">"g-sidenav-show bg-gray-100"</span><span class="nt">&gt;</span>

  <span class="nt">&lt;h1&gt;</span>Hello, world!<span class="nt">&lt;/h1&gt;</span>

  <span class="c">&lt;!--   Core JS Files   --&gt;</span>
  <span class="nt">&lt;script </span><span class="na">src=</span><span class="s">"../assets/js/core/popper.min.js"</span><span class="nt">&gt;&lt;/script&gt;</span>
  <span class="nt">&lt;script </span><span class="na">src=</span><span class="s">"../assets/js/core/bootstrap.min.js"</span><span class="nt">&gt;&lt;/script&gt;</span>

  <span class="c">&lt;!-- Plugin for the charts, full documentation here: https://www.chartjs.org/ --&gt;</span>
  <span class="nt">&lt;script </span><span class="na">src=</span><span class="s">"../assets/js/plugins/chartjs.min.js"</span><span class="nt">&gt;&lt;/script&gt;</span>
  <span class="nt">&lt;script </span><span class="na">src=</span><span class="s">"../assets/js/plugins/Chart.extension.js"</span><span class="nt">&gt;&lt;/script&gt;</span>

  <span class="c">&lt;!-- Control Center for Soft UI Dashboard: parallax effects, scripts for the example pages etc --&gt;</span>
  <span class="nt">&lt;script </span><span class="na">src=</span><span class="s">"../assets/js/soft-ui-dashboard.min.js"</span><span class="nt">&gt;&lt;/script&gt;</span>
<span class="nt">&lt;/body&gt;</span>

<span class="nt">&lt;/html&gt;</span></code></pre>
                </figure>
                <h2 id="important-globals">Important globals</h2>
                <p>Soft UI Dashboard employs a handful of important global styles and settings that you’ll need to be
                    aware of when using it, all of which are almost exclusively geared towards the
                    <em>normalization</em> of cross browser styles. Let’s dive in.</p>
                <h3 id="html5-doctype">HTML5 doctype</h3>
                <p>Bootstrap requires the use of the HTML5 doctype. Without it, you’ll see some funky incomplete
                    styling, but including it shouldn’t cause any considerable hiccups.</p>
                <figure class="highlight">
                    <pre><code class="language-html" data-lang="html"><span class="cp">&lt;!doctype html&gt;</span>
<span class="nt">&lt;html</span> <span class="na">lang=</span><span class="s">"en"</span><span class="nt">&gt;</span>
  ...
<span class="nt">&lt;/html&gt;</span></code></pre>
                </figure>
                <h3 id="responsive-meta-tag">Responsive meta tag</h3>
                <p>Bootstrap is developed <em>mobile first</em>, a strategy in which we optimize code for mobile devices
                    first and then scale up components as necessary using CSS media queries. To ensure proper rendering
                    and touch zooming for all devices, <strong>add the responsive viewport meta tag</strong> to your
                    <code class="language-plaintext highlighter-rouge">&lt;head&gt;</code>.</p>
                <figure class="highlight">
                    <pre><code class="language-html" data-lang="html"><span class="nt">&lt;meta</span> <span class="na">name=</span><span class="s">"viewport"</span> <span class="na">content=</span><span class="s">"width=device-width, initial-scale=1, shrink-to-fit=no"</span><span class="nt">&gt;</span></code></pre>
                </figure>
                <p>You can see an example of this in action in the <a href="#starter-template">starter template</a>.</p>
                <h3 id="bootstrap-components">Bootstrap components</h3>
                <p>Many of Bootstrap’s components and utilities are built with <code
                        class="language-plaintext highlighter-rouge">@each</code> loops that iterate over a Sass map.
                    This is especially helpful for generating variants of a component by our <code
                        class="language-plaintext highlighter-rouge">$theme-colors</code> and creating responsive
                    variants for each breakpoint. As you customize these Sass maps and recompile, you’ll automatically
                    see your changes reflected in these loops.</p>
                <h3 id="bootstrap-tutorial">Bootstrap tutorial</h3>
                <p>Please check our official <a href="https://www.youtube.com/channel/UCVyTG4sCw-rOvB9oHkzZD1w/videos"
                        rel="nofollow" target="_blank">Youtube channel</a> for more tutorials.</p>
            </main>
          </div>
          <div class="ct-docs-main-footer-row">
              <div class="ct-docs-main-footer-blank-col"></div>
              <div class="ct-docs-main-footer-col">
                  <footer class="ct-docs-footer">
                      <div class="ct-docs-footer-inner-row">
                          <div class="ct-docs-footer-col">
                              <div class="ct-docs-footer-copyright">
                                  ©
                                  <script>
                                      document.write(
                                          new Date().getFullYear()
                                      );
                                  </script>2021
                                  <a href="https://creative-tim.com" class="ct-docs-footer-copyright-author" target="_blank">Creative Tim</a>
                                  &amp;
                                  <a href="https://updivision.com" class="ct-docs-footer-copyright-author" target="_blank">UPDIVISION</a>
                              </div>
                          </div>
                          <div class="ct-docs-footer-col">
                              <ul class="ct-docs-footer-nav-footer">
                                  <li>
                                      <a href="https://creative-tim.com" class="ct-docs-footer-nav-link" target="_blank">Creative Tim</a>
                                  </li>
                                  <li>
                                      <a href="https://updivision.com" class="ct-docs-footer-nav-link" target="_blank">UPDIVISION</a>
                                  </li>
                                  <li>
                                      <a href="https://www.creative-tim.com/contact-us" class="ct-docs-footer-nav-link" target="_blank">Contact Us</a>
                                  </li>
                                  <li>
                                      <a href="https://creative-tim.com/blog" class="ct-docs-footer-nav-link" target="_blank">Blog</a>
                                  </li>
                              </ul>
                          </div>
                      </div>
                  </footer>
              </div>
          </div>
      </div>
      <script src="https://demos.creative-tim.com/argon-dashboard-pro/assets/vendor/jquery/dist/jquery.min.js" type="text/javascript"></script>
      <script src="https://demos.creative-tim.com/soft-ui-design-system-pro/assets/js/core/popper.min.js" type="text/javascript"></script>
      <script src="https://demos.creative-tim.com/soft-ui-design-system-pro/assets/js/core/bootstrap.min.js" type="text/javascript"></script>
      <script src="https://demos.creative-tim.com/argon-dashboard-pro/assets/vendor/prismjs/prism.js" type="text/javascript"></script>
      <script src="https://demos.creative-tim.com/argon-design-system-pro/assets/demo/docs.min.js" type="text/javascript"></script>
      <script src="https://demos.creative-tim.com/argon-design-system-pro/assets/demo/vendor/holder.min.js" type="text/javascript"></script>
      <script src="https://demos.creative-tim.com/soft-ui-design-system-pro/assets/js/plugins/moment.min.js" type="text/javascript"></script>
      <script src="https://demos.creative-tim.com/soft-ui-design-system-pro/assets/js/soft-design-system-pro.min.js?v=1.0.0" type="text/javascript"></script>
      <script>
          Holder.addTheme("gray", {
              bg: "#777",
              fg: "rgba(255,255,255,.75)",
              font: "Helvetica",
              fontweight: "normal",
          });
      </script>
      <script>
          // Facebook Pixel Code Don't Delete
          !(function (f, b, e, v, n, t, s) {
              if (f.fbq) return;
              n = f.fbq = function () {
                  n.callMethod
                      ? n.callMethod.apply(n, arguments)
                      : n.queue.push(arguments);
              };
              if (!f._fbq) f._fbq = n;
              n.push = n;
              n.loaded = !0;
              n.version = "2.0";
              n.queue = [];
              t = b.createElement(e);
              t.async = !0;
              t.src = v;
              s = b.getElementsByTagName(e)[0];
              s.parentNode.insertBefore(t, s);
          })(
              window,
              document,
              "script",
              "//connect.facebook.net/en_US/fbevents.js"
          );

          try {
              fbq("init", "111649226022273");
              fbq("track", "PageView");
          } catch (err) {
              console.log("Facebook Track Error:", err);
          }
      </script>
      <script src="../../../assets/docs.js"></script>
  </body>
  </html>
