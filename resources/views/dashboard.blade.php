@extends('layouts.user_type.auth')

@section('content')
    <div class="container-fluid">
        <div class="row mt-4">
            @if (eventInfo())
                <div class="col-lg-7 mb-lg-0 mb-4">
                    <div class="overflow-hidden position-relative border-radius-lg bg-cover h-100" style="background-image: url('{{ eventInfo()->banner_url }}');">
                        <span class="mask bg-gradient-dark"></span>
                        <div class="card-body position-relative z-index-1 d-flex flex-column h-100 p-3">
                            <h5 class="text-white mb-4 pt-2">Olá <span class="font-weight-bolder">{{ auth()->user()->name }}</span></h5>
                            <p class="text-white">Estamos trabalhando com as telas do evento <strong>{{ eventInfo()->title }}</strong>.</p>
                            <p class="text-white">Se precisar trabalhar com outro evento, pode alterar selecionando um dos eventos abaixo</p>
                        </div>
                    </div>
                </div>
            @else
                <div class="col-lg-5">
                    <div class="card h-100 p-3">
                        <div class="overflow-hidden position-relative border-radius-lg bg-cover h-100" style="background-image: url('https://www.swapcard.com/static/bg-57bc8287aba26508a8b8bb8fd080f110.png');">
                            <span class="mask bg-gradient-dark"></span>
                            <div class="card-body position-relative z-index-1 d-flex flex-column h-100 p-3">
                                <h5 class="text-white mb-4 pt-2">Olá <span class="font-weight-bolder">{{ auth()->user()->name }}</span></h5>
                                <p class="text-white">Comece escolhendo um envento para gerenciar o conteúdo das telas.</p>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
        <div class="row my-4">
            <div class="col-md-12 mb-md-0 mb-4">
                <div class="card">
                    <div class="card-header pb-0">
                        <div class="row">
                            <div class="col-lg-6 col-7">
                                <h6>Lista de eventos ({{ $events->total() }} eventos)</h6>
                                <p class="text-sm mb-0">
                                    <i class="fa fa-check text-info" aria-hidden="true"></i>
                                    <span class="font-weight-bold ms-1">Eventos ativos são mostrados primeiro</span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="card-body px-0 pb-2">
                        <div class="table-responsive">
                            <table class="table align-items-center mb-0">
                                <thead>
                                    <tr>
                                        <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Evento</th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($events as $event)
                                    @if (
                                        !str_contains('deletar', strtolower($event->title)) ||
                                        !str_contains('excluir', strtolower($event->title)) ||
                                        !str_contains('deleted', strtolower($event->title))
                                    )
                                    <tr class="{{ $event->is_live ? '' : 'opacity-6' }}" style="{{ $event->is_live ? '' : 'opacity: 0.6;' }}">
                                        <td>
                                            <div class="d-flex px-2 py-1">
                                                <div>
                                                    @if ($event->banner_url)
                                                        <img src="{{ $event->banner_url }}" class="avatar avatar-sm me-3">
                                                    @else
                                                        <img src="{{ asset('assets/img/curved-images/white-curved.jpeg') }}" class="avatar avatar-sm me-3">
                                                    @endif
                                                </div>
                                                <div class="d-flex flex-column justify-content-center">
                                                    <h6 class="mb-0 text-sm">
                                                        <a href="{{ route('select.event', $event->id) }}">
                                                            {{ $event->title }}
                                                            @if (!$event->is_live)
                                                                <span class="badge badge-sm bg-gradient-secondary">Offline</span>
                                                            @endif
                                                        </a>
                                                    </h6>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="align-right">
                                            <a href="{{ route('select.event', $event->id) }}" class="btn btn-sm btn-primary">
                                                Selecionar
                                            </a>
                                        </td>
                                    </tr>
                                    @endif
                                    @endforeach
                                </tbody>
                            </table>

                            <!-- Paginação simplificada -->
                            <div class="d-flex justify-content-between align-items-center mt-4 px-4">
                                <div>
                                    <p class="text-sm text-muted">
                                        Mostrando {{ $events->firstItem() ?? 0 }} a {{ $events->lastItem() ?? 0 }} de {{ $events->total() }} resultados
                                    </p>
                                </div>
                                <div class="d-flex">
                                    @if ($events->onFirstPage())
                                        <button class="btn btn-sm btn-outline-secondary disabled me-2">
                                            <i class="fa fa-chevron-left"></i> Anterior
                                        </button>
                                    @else
                                        <a href="{{ $events->previousPageUrl() }}" class="btn btn-sm btn-outline-primary me-2">
                                            <i class="fa fa-chevron-left"></i> Anterior
                                        </a>
                                    @endif

                                    @if ($events->hasMorePages())
                                        <a href="{{ $events->nextPageUrl() }}" class="btn btn-sm btn-outline-primary">
                                            Próximo <i class="fa fa-chevron-right"></i>
                                        </a>
                                    @else
                                        <button class="btn btn-sm btn-outline-secondary disabled">
                                            Próximo <i class="fa fa-chevron-right"></i>
                                        </button>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    @endsection
    @push('dashboard')
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            var canvasBars = document.getElementById("chart-bars");
            if (canvasBars) {
                var ctx = canvasBars.getContext("2d");

                new Chart(ctx, {
                    type: "bar",
                    data: {
                        labels: ["Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
                        datasets: [{
                            label: "Sales",
                            tension: 0.4,
                            borderWidth: 0,
                            borderRadius: 4,
                            borderSkipped: false,
                            backgroundColor: "#fff",
                            data: [450, 200, 100, 220, 500, 100, 400, 230, 500],
                            maxBarThickness: 6
                        }],
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false,
                            }
                        },
                        interaction: {
                            intersect: false,
                            mode: 'index',
                        },
                        scales: {
                            y: {
                                grid: {
                                    drawBorder: false,
                                    display: false,
                                    drawOnChartArea: false,
                                    drawTicks: false,
                                },
                                ticks: {
                                    suggestedMin: 0,
                                    suggestedMax: 500,
                                    beginAtZero: true,
                                    padding: 15,
                                    font: {
                                        size: 14,
                                        family: "Open Sans",
                                        style: 'normal',
                                        lineHeight: 2
                                    },
                                    color: "#fff"
                                },
                            },
                            x: {
                                grid: {
                                    drawBorder: false,
                                    display: false,
                                    drawOnChartArea: false,
                                    drawTicks: false
                                },
                                ticks: {
                                    display: false
                                },
                            },
                        },
                    },
                });
            } else {
                console.error("Elemento #chart-bars não encontrado no DOM.");
            }

            var canvasLine = document.getElementById("chart-line");
            if (canvasLine) {
                var ctx2 = canvasLine.getContext("2d");

                var gradientStroke1 = ctx2.createLinearGradient(0, 230, 0, 50);
                gradientStroke1.addColorStop(1, 'rgba(203,12,159,0.2)');
                gradientStroke1.addColorStop(0.2, 'rgba(72,72,176,0.0)');
                gradientStroke1.addColorStop(0, 'rgba(203,12,159,0)');

                var gradientStroke2 = ctx2.createLinearGradient(0, 230, 0, 50);
                gradientStroke2.addColorStop(1, 'rgba(20,23,39,0.2)');
                gradientStroke2.addColorStop(0.2, 'rgba(72,72,176,0.0)');
                gradientStroke2.addColorStop(0, 'rgba(20,23,39,0)');

                new Chart(ctx2, {
                    type: "line",
                    data: {
                        labels: ["Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
                        datasets: [{
                                label: "Mobile apps",
                                tension: 0.4,
                                borderWidth: 0,
                                pointRadius: 0,
                                borderColor: "#cb0c9f",
                                borderWidth: 3,
                                backgroundColor: gradientStroke1,
                                fill: true,
                                data: [50, 40, 300, 220, 500, 250, 400, 230, 500],
                                maxBarThickness: 6
                            },
                            {
                                label: "Websites",
                                tension: 0.4,
                                borderWidth: 0,
                                pointRadius: 0,
                                borderColor: "#3A416F",
                                borderWidth: 3,
                                backgroundColor: gradientStroke2,
                                fill: true,
                                data: [30, 90, 40, 140, 290, 290, 340, 230, 400],
                                maxBarThickness: 6
                            },
                        ],
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false,
                            }
                        },
                        interaction: {
                            intersect: false,
                            mode: 'index',
                        },
                        scales: {
                            y: {
                                grid: {
                                    drawBorder: false,
                                    display: true,
                                    drawOnChartArea: true,
                                    drawTicks: false,
                                    borderDash: [5, 5]
                                },
                                ticks: {
                                    display: true,
                                    padding: 10,
                                    color: '#b2b9bf',
                                    font: {
                                        size: 11,
                                        family: "Open Sans",
                                        style: 'normal',
                                        lineHeight: 2
                                    },
                                }
                            },
                            x: {
                                grid: {
                                    drawBorder: false,
                                    display: false,
                                    drawOnChartArea: false,
                                    drawTicks: false,
                                    borderDash: [5, 5]
                                },
                                ticks: {
                                    display: true,
                                    color: '#b2b9bf',
                                    padding: 20,
                                    font: {
                                        size: 11,
                                        family: "Open Sans",
                                        style: 'normal',
                                        lineHeight: 2
                                    },
                                }
                            },
                        },
                    },
                });
            } else {
                console.error("Elemento #chart-line não encontrado no DOM.");
            }
        });
    </script>
    @endpush

