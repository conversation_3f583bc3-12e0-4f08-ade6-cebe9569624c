<div class="row">
    <div class="col-md-6 col-xs-12">
        <label>Tipo de con<PERSON></label>
        <select name="type" id="content-type" class="form-control" onchange="listner()" required>
            @foreach ($itemTypes as $type)
                <option
                    value="{{ $type }}"
                    @if ($item)
                        {{ $item->type == $type ? 'selected' : null  }}
                    @endif
                >{{ $type }}</option>
            @endforeach
        </select>
    </div>
    <div class="col-md-6 col-xs-12">
        <label>Tempo de exposição (em segundos)</label>
        <input type="number" class="form-control" name="time" value="{{ $item->time ?? 5 }}" required>
    </div>
    <div class="col-md-6 col-xs-12" id="place-selector" style="display: none">
        <div class="mt-3">
            <label>Localização das palestras (dentro da swapcard)</label>
            <select name="swap_place_id" class="form-control">
            @foreach ($places as $place)
                <option
                    value="{{ $place['id'] }}"
                    @if ($item && $item->swap_place_id == $place['id']) selected @endif
                >{{ $place['name'] }}</option>
            @endforeach
        </select>
        </div>
    </div>
    <div class="col-md-12 col-xs-12" id="media-upload" style="display: none">
        <div class="row mt-3">
            @if ($item)
                <div class="col-3">
                    <img src="{{ $item->media_url }}" style="height: 120px">
                </div>
                <div class="col-9">
                    <div class="mt-3">
                        <label for="media_file">Arquivo</label>
                        <br>
                        @if ($item->media_url)
                            <input type="file" name="media_file" id="media_file" enctype="multipart/form-data">
                        @else
                            <input type="file" name="media_file" id="media_file" enctype="multipart/form-data">
                        @endif
                    </div>
                </div>
            @else
                <div class="col-9">
                    <div class="mt-3">
                        <label for="media_file">Arquivo</label>
                        <br>
                        <input type="file" name="media_file" id="media_file">
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
<div class="d-flex justify-content-end">
    <button type="submit" class="btn bg-gradient-dark my-4 mb-2">{{ $button }}</button>
</div>

@push('scripts')
    <script>
        function listner(){
            changeFileVisible()
            changePlaceVisible()
        }

        function changeFileVisible(){
            const contentType = document.getElementById('content-type');
            const mediaUpload = document.getElementById('media-upload');
            const mediaFileInput = document.getElementById('media_file');

            if (contentType.value === 'banner') {
                mediaUpload.style.display = 'block';
                if (mediaFileInput) {
                    mediaFileInput.required = true;
                }
            } else {
                mediaUpload.style.display = 'none';
                if (mediaFileInput) {
                    mediaFileInput.required = false;
                }
            }
        }

        function changePlaceVisible(){
            const contentType = document.getElementById('content-type');
            const placeSelector = document.getElementById('place-selector');
            if (contentType.value === 'agenda') {
                placeSelector.style.display = 'block';
            } else {
                placeSelector.style.display = 'none';
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            listner();
        });
    </script>
@endpush
