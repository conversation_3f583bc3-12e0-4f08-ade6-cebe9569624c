@extends('layouts.user_type.auth')

@section('content')

<main class="main-content position-relative max-height-vh-100 h-100 mt-1 border-radius-lg ">
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="card mb-4">
                    <div class="card-header pb-0">
                        <div class="d-flex justify-content-between">
                            <h6>Conteúdo do canal: <strong>{{ $channel->title }}</strong></h6>
                            <a class="btn btn-sm btn-outline-primary" href="{{ route('channel.item.create', ['channel' => $channel->id]) }}">
                                Adicionar conteúdo
                            </a>
                        </div>
                    </div>
                    <div class="card-body px-4 pt-0 pb-2">
                        <div class="table-responsive p-0">
                            <v-channel-item-list
                                :item-list="{{ $channel->items }}"
                                :channel-id="{{ $channel->id }}">
                            </v-channel-item-list>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

@endsection
