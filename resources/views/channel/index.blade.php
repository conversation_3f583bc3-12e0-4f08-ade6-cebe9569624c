@extends('layouts.user_type.auth')

@section('content')

<main class="main-content position-relative max-height-vh-100 h-100 mt-1 border-radius-lg ">
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="card mb-4">
                    <div class="card-header pb-0 d-flex justify-content-between">
                        <h6>Lista de canais</h6>
                        @if ($eventDate)
                            <a class="btn btn-primary" href="{{ route('channel.create') }}">Cadastrar canal</a>
                        @endif
                    </div>
                    <div class="card-body px-4 pt-0 pb-2">
                        <form action="{{ route('congress.date.update.store') }}" class="row" method="post">
                            @csrf
                            <div class="col-md-4">
                                <label>Início do congresso</label>
                                <input type="date" class="form-control" name="start_date" value="{{ $eventDate->start_date ?? null }}" required>
                            </div>
                            <div class="col-md-4">
                                <label>Fim do congresso</label>
                                <input type="date" class="form-control" name="end_date" value="{{ $eventDate->end_date ?? null }}" required>
                            </div>
                            <div class="col-md-4">
                                <label>&nbsp;</label>
                                <br>
                                <button class="btn btn-outline-primary" type="submit">Salvar</button>
                            </div>
                        </form>
                        @if ($eventDate)
                            <div class="table-responsive p-0">
                                <table class="table align-items-center mb-0">
                                    <thead>
                                        <tr>
                                            <th class="text-uppercase text-secondary opacity-7">Nome</th>
                                            <th class="text-secondary opacity-7"></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($channels as $item)
                                            <tr>
                                                <td class="px-4">
                                                    {{ $item->title }}
                                                    {{-- <p class="text-xs text-secondary mb-0">Organization</p> --}}
                                                </td>
                                                <td class="d-flex justify-content-end">
                                                    <a
                                                        class="btn btn-link text-danger text-gradient px-3 mb-0"
                                                        onclick="return confirm('Tem certeza?')"
                                                        href="{{ route('channel.delete', $item->id) }}"
                                                    >
                                                        <i class="far fa-trash-alt me-2" aria-hidden="true"></i>Remover
                                                    </a>
                                                    <a
                                                        class="btn btn-link text-dark px-3 mb-0"
                                                        href="{{ route('channel.edit', $item->id) }}"
                                                    >
                                                        <i class="fas fa-pencil-alt text-dark me-2" aria-hidden="true"></i>Editar
                                                    </a>
                                                    <a
                                                        class="btn btn-link text-dark px-3 mb-0"
                                                        href="{{ route('channel.item.index', $item->id) }}"
                                                    >
                                                        <i class="fas fa-regular fa-box-open text-dark me-2" aria-hidden="true"></i>Conteúdo
                                                    </a>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

@endsection
