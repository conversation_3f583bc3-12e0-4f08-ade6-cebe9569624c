<template>
  <span>
      <table class="table align-items-center mb-0">
        <thead>
          <tr>
            <th></th>
            <th class="text-uppercase text-secondary opacity-7">Ordem</th>
            <th class="text-uppercase text-secondary opacity-7">Con<PERSON><PERSON><PERSON></th>
            <th class="text-uppercase text-secondary opacity-7">Tipo de conteúdo</th>
            <th class="text-uppercase text-secondary opacity-7">Tempo de exibição</th>
            <th class="text-secondary opacity-7"></th>
          </tr>
        </thead>
        <tr v-if="load">
            <td colspan="6" class="text-center">
                <i class="fas fa-spin fa-spinner"></i> Carregando...
            </td>
        </tr>
        <draggable v-else v-model="myArray" @change="updateDrag" tag="tbody" handle=".moveitem">
          <tr v-for="(item, index) in myArray" :key="index">
            <td class="px-4">
              <i class="ni ni-bullet-list-67 moveitem" style="cursor: move"></i>
            </td>
            <td class="px-4">
              <span class="badge bg-info">{{ index+1 }}</span>
            </td>
            <td class="px-4">
              <img v-if="item.type === 'banner'" :src="item.media_url" style="height: 80px">
            </td>
            <td class="px-4">
              {{ item.type }}
              <span v-if="item.type === 'agenda'" style="font-size: 9px">
                <br>
                Swapcard: {{ item.swap_place_name }}
              </span>
            </td>
            <td class="px-4">{{ item.time }}s</td>
            <td class="px-4">
              <a
                class="btn btn-link text-danger text-gradient px-3 mb-0"
                onclick="return confirm('Tem certeza?')"
                :href="`/channel/item/${channelId}/delete/${item.id}`"
              >
                <i class="far fa-trash-alt me-2" aria-hidden="true"></i>Remover
              </a>
              <a
                class="btn btn-link text-dark px-3 mb-0"
                :href="`/channel/item/${channelId}/edit/${item.id}`"
              >
                <i class="fas fa-pencil-alt text-dark me-2" aria-hidden="true"></i>Editar
              </a>
            </td>
          </tr>
        </draggable>
      </table>
  </span>
</template>
<script>
  import axios from "axios";
import { capitalize } from "lodash";
import draggable from "vuedraggable";
  export default {
    name: 'ChannelItemList',
    props: ['itemList', 'channelId'],
    data() {
      return {
        myArray: this.itemList,
        load: false,
      }
    },
    components: {
      draggable,
    },
    methods: {
      updateDrag(evt) {
        if (evt.moved) {
          this.load = true;
          axios.post(`/channel/item/${this.channelId}/update-order`, { items: this.myArray })
          .then((response) => {
            console.log(response.data);
          })
          .finally(() => {
            this.load = false;
          });
        }
      }
    }
  }
</script>
